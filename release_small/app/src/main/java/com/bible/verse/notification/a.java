package com.bible.verse.notification;

import android.app.Application;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.graphics.drawable.ColorDrawable;
import android.os.Build$VERSION;
import android.os.Bundle;
import android.view.View;
import android.view.View$OnClickListener;
import android.view.Window;
import android.view.WindowManager$LayoutParams;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.view.animation.Animation$AnimationListener;
import android.view.animation.AnimationSet;
import android.view.animation.TranslateAnimation;
import android.widget.TextView;
import com.bible.verse.KinjMainActivity;
import com.bible.verse.widget.KinjSwipeLayout$a;
import jj.c;
import k4.w;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import o4.e0;
import oj.g0;
import org.jetbrains.annotations.NotNull;
import p4.p;
import s4.e;
import si.g;
import si.h;

/* compiled from: FloatDialog.kt */
/* loaded from: classes2.dex */
public final class a extends Dialog {

    @NotNull
    public static final a$a w = new a$a(null);
    public static int x = -1;
    public static a y;

    @NotNull
    public final p4.a n;

    @NotNull
    public final p u;

    @NotNull
    public final g v;

    public a(Context context, p4.a aVar, p pVar) {
        super(context);
        this.n = aVar;
        this.u = pVar;
        this.v = h.a(new a$c(this));
    }

    public /* synthetic */ a(Context context, p4.a aVar, p pVar, DefaultConstructorMarker defaultConstructorMarker) {
        this(context, aVar, pVar);
    }

    public static /* synthetic */ void h(a aVar, boolean z, int i, Object obj) {
        if ((i & 1) != 0) {
            z = true;
        }
        aVar.g(z);
    }

    public static final void n(a this$0, View view) {
        Intrinsics.checkNotNullParameter(this$0, "this$0");
        this$0.i();
    }

    public static final void q(a this$0, View view) {
        Intrinsics.checkNotNullParameter(this$0, "this$0");
        h(this$0, false, 1, null);
        this$0.i();
    }

    public static final void r(a this$0, View view) {
        Intrinsics.checkNotNullParameter(this$0, "this$0");
        this$0.k();
    }

    public final void g(boolean z) {
        if (z) {
            e.a.l("kinj_push_float_close");
        }
        b bVar = b.a;
        Application applicationA = com.blankj.utilcode.util.e.a();
        Intrinsics.checkNotNullExpressionValue(applicationA, "getApp(...)");
        bVar.s(applicationA, this.u.h());
    }

    public final void i() {
        View viewFindViewById;
        if (isShowing() && (viewFindViewById = findViewById(2131363254)) != null) {
            l(viewFindViewById, 300L, true, new Animation$AnimationListener() { // from class: com.bible.verse.notification.a$d
                @Override // android.view.animation.Animation$AnimationListener
                public void onAnimationEnd(@NotNull Animation animation) {
                    Intrinsics.checkNotNullParameter(animation, "animation");
                    this.n.dismiss();
                }

                @Override // android.view.animation.Animation$AnimationListener
                public void onAnimationRepeat(@NotNull Animation animation) {
                    Intrinsics.checkNotNullParameter(animation, "animation");
                }

                @Override // android.view.animation.Animation$AnimationListener
                public void onAnimationStart(@NotNull Animation animation) {
                    Intrinsics.checkNotNullParameter(animation, "animation");
                }
            });
        }
    }

    public final m4.b j() {
        return (m4.b) this.v.getValue();
    }

    public final void k() {
        w wVar = w.a;
        if (wVar.a()) {
            int iE = c.n.e(5);
            if (iE == 0) {
                wVar.d(110);
            } else if (iE == 1) {
                wVar.f("11");
            } else if (iE == 2) {
                wVar.e(10L);
            } else if (iE == 3) {
                wVar.f("115");
            } else if (iE == 4) {
                wVar.d(12);
            }
        } else {
            g(false);
        }
        p4.a aVar = this.n;
        Intent intent = new Intent(com.blankj.utilcode.util.e.a(), (Class<?>) KinjMainActivity.class);
        intent.addFlags(268435456);
        Bundle bundleD = this.u.d();
        intent.putExtra("push_other_params", (Bundle) (bundleD != null ? bundleD.clone() : null));
        intent.putExtra("push_host_type", e0.w);
        intent.putExtra("push_nav_from", aVar.s());
        intent.putExtra("push_nav_to", aVar.r());
        intent.putExtra("push_id", this.u.h());
        com.blankj.utilcode.util.e.a().startActivity(intent);
        e eVar = e.a;
        eVar.o("kinj_push_float_click", eVar.g(aVar.s(), true));
        i();
    }

    public final void l(View view, long j, boolean z, Animation$AnimationListener animation$AnimationListener) {
        if (view == null) {
            return;
        }
        view.setVisibility(4);
        TranslateAnimation translateAnimation = new TranslateAnimation(1, 0.0f, 1, 0.0f, 1, 0.0f, 1, -1.0f);
        AnimationSet animationSet = new AnimationSet(true);
        animationSet.setFillAfter(true);
        animationSet.setDuration(j);
        if (z) {
            animationSet.addAnimation(new AlphaAnimation(1.0f, 0.0f));
        }
        animationSet.addAnimation(translateAnimation);
        animationSet.setAnimationListener(animation$AnimationListener);
        view.setAnimation(animationSet);
        view.startAnimation(animationSet);
    }

    public final void m(View view, long j) {
        if (view == null) {
            return;
        }
        view.setVisibility(4);
        TranslateAnimation translateAnimation = new TranslateAnimation(1, 0.0f, 1, 0.0f, 1, -1.0f, 1, 0.0f);
        AlphaAnimation alphaAnimation = new AlphaAnimation(0.0f, 1.0f);
        AnimationSet animationSet = new AnimationSet(true);
        animationSet.setFillAfter(true);
        animationSet.setDuration(j);
        animationSet.addAnimation(translateAnimation);
        animationSet.addAnimation(alphaAnimation);
        view.setAnimation(animationSet);
        view.startAnimation(animationSet);
    }

    public final void o(boolean z) {
        if (!z) {
            e eVar = e.a;
            eVar.o("kinj_push_float_show", eVar.g(this.n.s(), true));
        }
        int i = a$b.a[this.n.s().ordinal()];
        if (i == 1 || i != 2) {
            p();
        } else {
            p();
        }
        m(j().c, 300L);
        oj.g.d(g0.b(), null, null, new a$f(this, null), 3, null);
        x = this.u.h();
    }

    @Override // android.app.Dialog
    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        requestWindowFeature(1);
        setCanceledOnTouchOutside(false);
        setCancelable(false);
        Window window = getWindow();
        Intrinsics.b(window);
        window.setGravity(48);
        Window window2 = getWindow();
        Intrinsics.b(window2);
        window2.setBackgroundDrawable(new ColorDrawable(0));
        Window window3 = getWindow();
        Intrinsics.b(window3)
        window3.setFlags(32, 32);
        Window window4 = getWindow();
        Intrinsics.b(window4);
        window4.setFlags(262144, 262144);
        setContentView(j().getRoot());
        Window window5 = getWindow();
        Intrinsics.b(window5);
        WindowManager$LayoutParams attributes = window5.getAttributes();
        attributes.dimAmount = 0.0f;
        attributes.width = -1;
        if (Build$VERSION.SDK_INT >= 26) {
            Window window6 = getWindow();
            Intrinsics.b(window6);
            window6.setType(2038);
        } else {
            Window window7 = getWindow();
            Intrinsics.b(window7);
            window7.setType(2003);
        }
        j().b.setOnClickListener(new View$OnClickListener() { // from class: o4.b
            @Override // android.view.View$OnClickListener
            public final void onClick(View view) {
                com.bible.verse.notification.a.n(this.n, view);
            }
        });
        j().c.setCallback(new KinjSwipeLayout$a() { // from class: com.bible.verse.notification.a$e
            @Override // com.bible.verse.widget.KinjSwipeLayout$a
            public void onClose() {
                a.h(this.a, false, 1, null);
                this.a.i();
            }
        });
        o(false);
    }

    public final void p() {
        m4.c cVarInflate = m4.c.inflate(getLayoutInflater());
        Intrinsics.checkNotNullExpressionValue(cVarInflate, "inflate(...)");
        cVarInflate.e.setOnClickListener(new View$OnClickListener() { // from class: o4.c
            @Override // android.view.View$OnClickListener
            public final void onClick(View view) {
                com.bible.verse.notification.a.q(this.n, view);
            }
        });
        j().c.addView(cVarInflate.getRoot());
        cVarInflate.getRoot().setOnClickListener(new View$OnClickListener() { // from class: o4.a
            @Override // android.view.View$OnClickListener
            public final void onClick(View view) {
                com.bible.verse.notification.a.r(this.n, view);
            }
        });
        w wVar = w.a;
        int iC = wVar.c(true);
        if (iC == 1) {
            try {
                p pVar = this.u;
                cVarInflate.f.setText(pVar.f());
                cVarInflate.j.setText(pVar.j());
                TextView textView = cVarInflate.i;
                Bundle bundleD = pVar.d();
                textView.setText(bundleD != null ? bundleD.getString("KEY_BTN") : null);
                com.bumptech.glide.b.u(cVarInflate.g).q(s4.h.a.h(pVar.g())).y0(cVarInflate.g);
                com.bumptech.glide.b.u(cVarInflate.d).p(Integer.valueOf(pVar.c())).y0(cVarInflate.d);
                return;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if (iC != 2) {
            return;
        }
        switch (c.n.e(10)) {
            case 0:
                wVar.d(110);
                break;
            case 1:
                wVar.e(10L);
                break;
            case 2:
                wVar.f("11");
                break;
            case 3:
                wVar.d(12);
                break;
            case 4:
                wVar.d(13);
                break;
            case 5:
                wVar.e(14L);
                break;
            case 6:
                wVar.d(145);
                break;
            case 8:
                wVar.f("115");
                break;
            case 9:
                wVar.d(135);
                break;
        }
    }
}
