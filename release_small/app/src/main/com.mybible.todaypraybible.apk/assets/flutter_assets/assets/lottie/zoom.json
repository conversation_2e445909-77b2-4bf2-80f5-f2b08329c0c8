{"v": "5.9.4", "fr": 25, "ip": 0, "op": 25, "w": 170, "h": 170, "nm": "合成 1", "ddd": 0, "assets": [{"id": "image_0", "w": 193, "h": 192, "u": "", "p": "data:image/png;base64,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", "e": 1}], "layers": [{"ddd": 0, "ind": 1, "ty": 2, "nm": "Group 1577.png", "cl": "png", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [85, 85, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [96.5, 96, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.51, 0.51, 0.51], "y": [1, 1, 1]}, "o": {"x": [0.49, 0.49, 0.49], "y": [0, 0, 0]}, "t": 0, "s": [89, 89, 100]}, {"i": {"x": [0.51, 0.51, 0.51], "y": [1, 1, 1]}, "o": {"x": [0.49, 0.49, 0.49], "y": [0, 0, 0]}, "t": 12, "s": [70, 70, 100]}, {"t": 24, "s": [89, 89, 100]}], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 25, "st": 0, "bm": 0}], "markers": []}