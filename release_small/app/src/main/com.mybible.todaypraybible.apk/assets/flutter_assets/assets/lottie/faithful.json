{"v": "5.7.3", "fr": 25, "ip": 0, "op": 50, "w": 1080, "h": 1080, "nm": "amen", "ddd": 0, "assets": [{"id": "image_0", "w": 124, "h": 132, "u": "", "p": "data:image/png;base64,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", "e": 1}, {"id": "image_1", "w": 435, "h": 448, "u": "", "p": "data:image/png;base64,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", "e": 1}], "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "空 1", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [540, 540, 0], "ix": 2}, "a": {"a": 0, "k": [50, 50, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.5, 0.5, 0.5], "y": [1, 1, 1]}, "o": {"x": [0.5, 0.5, 0.5], "y": [0, 0, 0]}, "t": 35, "s": [100, 100, 100]}, {"t": 49, "s": [0, 0, 100]}], "ix": 6}}, "ao": 0, "ip": 30, "op": 50, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 2, "nm": "star.png", "cl": "png", "parent": 1, "refId": "image_0", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 15, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 25, "s": [0]}, {"t": 40, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-79, -301, 0], "ix": 2}, "a": {"a": 0, "k": [62, 66, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 75, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 2, "nm": "star.png", "cl": "png", "parent": 1, "refId": "image_0", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 35, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 45, "s": [0]}, {"t": 60, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [145, 389, 0], "ix": 2}, "a": {"a": 0, "k": [62, 66, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 75, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 2, "nm": "star.png", "cl": "png", "parent": 1, "refId": "image_0", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 21, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 36, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 46, "s": [0]}, {"t": 61, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [343.003, 77.03, 0], "ix": 2}, "a": {"a": 0, "k": [62, 66, 0], "ix": 1}, "s": {"a": 0, "k": [108.119, 108.119, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 75, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 2, "nm": "star.png", "cl": "png", "parent": 1, "refId": "image_0", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 25, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 50, "s": [0]}, {"t": 65, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [261, -105, 0], "ix": 2}, "a": {"a": 0, "k": [62, 66, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 75, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 2, "nm": "star.png", "cl": "png", "parent": 1, "refId": "image_0", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 13, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 28, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 38, "s": [0]}, {"t": 53, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-91, 257, 0], "ix": 2}, "a": {"a": 0, "k": [62, 66, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 75, "st": 0, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 2, "nm": "star.png", "cl": "png", "parent": 1, "refId": "image_0", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 45, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 60, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 70, "s": [0]}, {"t": 85, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [317, 21, 0], "ix": 2}, "a": {"a": 0, "k": [62, 66, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 75, "st": 0, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 2, "nm": "star.png", "cl": "png", "parent": 1, "refId": "image_0", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 4, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 19, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 29, "s": [0]}, {"t": 44, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-197, -41, 0], "ix": 2}, "a": {"a": 0, "k": [62, 66, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 75, "st": 0, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 2, "nm": "faithful2.png", "cl": "png", "parent": 1, "refId": "image_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [67, 46, 0], "ix": 2}, "a": {"a": 0, "k": [217.5, 224, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 7, "s": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 15, "s": [120, 120, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 20, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 25, "s": [110, 110, 100]}, {"t": 30, "s": [100, 100, 100]}], "ix": 6}}, "ao": 0, "ip": 0, "op": 75, "st": 0, "bm": 0}], "markers": []}